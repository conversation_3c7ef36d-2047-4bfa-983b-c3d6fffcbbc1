<template>
  <div class="dashboard-container">
    <el-container>
      <!-- 头部导航栏 -->
      <el-header class="header">
        <div class="header-left">
          <div class="logo-section">
            <span class="logo-text">爱收藏管理后台</span>
          </div>
        </div>
        <div class="header-right">
          <div class="header-actions">
            <span class="welcome-text">欢迎，{{ currentUser?.account || '管理员' }}</span>
            <el-dropdown @command="handleCommand" class="user-dropdown">
              <el-button type="text" class="user-btn">
                <el-icon><User /></el-icon>
                <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">个人资料</el-dropdown-item>
                  <el-dropdown-item command="settings">账户设置</el-dropdown-item>
                  <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </el-header>

      <el-container>
        <!-- 侧边栏 -->
        <el-aside width="200px" class="sidebar">
          <el-menu
            :default-active="activeMenu"
            class="sidebar-menu"
            @select="handleMenuSelect"
            router
          >
            <el-menu-item index="/" class="menu-item">
              <el-icon><House /></el-icon>
              <template #title>首页</template>
            </el-menu-item>
            <el-menu-item index="/users/list" class="menu-item">
              <el-icon><User /></el-icon>
              <template #title>用户管理</template>
            </el-menu-item>
            <el-menu-item index="/content/list" class="menu-item">
              <el-icon><Document /></el-icon>
              <template #title>内容管理</template>
            </el-menu-item>
            <el-menu-item index="/analytics" class="menu-item">
              <el-icon><DataBoard /></el-icon>
              <template #title>数据分析</template>
            </el-menu-item>
            <el-menu-item index="/settings" class="menu-item">
              <el-icon><Setting /></el-icon>
              <template #title>系统设置</template>
            </el-menu-item>
          </el-menu>
        </el-aside>

        <!-- 主内容区 -->
        <el-main class="main-content">
          <!-- 统计卡片区域 -->
          <div class="stats-grid">
            <el-card class="stats-card" shadow="hover">
              <div class="stats-content">
                <div class="stats-icon users-icon">
                  <el-icon size="24"><User /></el-icon>
                </div>
                <div class="stats-info">
                  <h3 class="stats-number">1,234</h3>
                  <p class="stats-label">总用户数</p>
                  <span class="stats-trend positive">
                    <el-icon><TrendCharts /></el-icon>
                    +12.5%
                  </span>
                </div>
              </div>
            </el-card>

            <el-card class="stats-card" shadow="hover">
              <div class="stats-content">
                <div class="stats-icon content-icon">
                  <el-icon size="24"><Document /></el-icon>
                </div>
                <div class="stats-info">
                  <h3 class="stats-number">5,678</h3>
                  <p class="stats-label">内容总数</p>
                  <span class="stats-trend positive">
                    <el-icon><TrendCharts /></el-icon>
                    +8.3%
                  </span>
                </div>
              </div>
            </el-card>

            <el-card class="stats-card" shadow="hover">
              <div class="stats-content">
                <div class="stats-icon revenue-icon">
                  <el-icon size="24"><Money /></el-icon>
                </div>
                <div class="stats-info">
                  <h3 class="stats-number">¥89,012</h3>
                  <p class="stats-label">本月收入</p>
                  <span class="stats-trend positive">
                    <el-icon><TrendCharts /></el-icon>
                    +15.2%
                  </span>
                </div>
              </div>
            </el-card>

            <el-card class="stats-card" shadow="hover">
              <div class="stats-content">
                <div class="stats-icon activity-icon">
                  <el-icon size="24"><DataLine /></el-icon>
                </div>
                <div class="stats-info">
                  <h3 class="stats-number">98.5%</h3>
                  <p class="stats-label">系统活跃度</p>
                  <span class="stats-trend positive">
                    <el-icon><TrendCharts /></el-icon>
                    +2.1%
                  </span>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 欢迎卡片 -->
          <div class="welcome-section">
            <el-card class="welcome-card" shadow="never">
              <div class="welcome-content">
                <div class="welcome-text">
                  <h2 class="welcome-title">欢迎回来，{{ currentUser?.account || '管理员' }}！</h2>
                  <p class="welcome-subtitle">今天是个美好的一天，让我们开始工作吧</p>
                  <div class="welcome-info">
                    <div class="info-item">
                      <span class="info-label">用户ID:</span>
                      <span class="info-value">{{ currentUser?.user_id }}</span>
                    </div>
                    <div class="info-item">
                      <span class="info-label">最后登录:</span>
                      <span class="info-value">{{ formatDate(new Date()) }}</span>
                    </div>
                  </div>
                </div>
                <div class="welcome-illustration">
                  <el-icon size="120" class="welcome-icon"><DataBoard /></el-icon>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 快速操作区域 -->
          <div class="quick-actions">
            <h3 class="section-title">快速操作</h3>
            <div class="actions-grid">
              <el-card class="action-card" shadow="hover">
                <div class="action-content">
                  <el-icon class="action-icon" size="32"><Plus /></el-icon>
                  <h4>添加用户</h4>
                  <p>快速添加新用户</p>
                </div>
              </el-card>
              <el-card class="action-card" shadow="hover">
                <div class="action-content">
                  <el-icon class="action-icon" size="32"><Edit /></el-icon>
                  <h4>内容管理</h4>
                  <p>管理系统内容</p>
                </div>
              </el-card>
              <el-card class="action-card" shadow="hover">
                <div class="action-content">
                  <el-icon class="action-icon" size="32"><View /></el-icon>
                  <h4>数据报表</h4>
                  <p>查看详细报表</p>
                </div>
              </el-card>
              <el-card class="action-card" shadow="hover">
                <div class="action-content">
                  <el-icon class="action-icon" size="32"><Setting /></el-icon>
                  <h4>系统设置</h4>
                  <p>配置系统参数</p>
                </div>
              </el-card>
            </div>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  User, ArrowDown, House, Document, DataBoard, Setting
} from '@element-plus/icons-vue'
import { getCurrentAdmin, adminLogout } from '@/api/auth'

const router = useRouter()
const route = useRoute()

// 当前用户信息
const currentUser = ref(null)

// 当前激活的菜单项
const activeMenu = computed(() => {
  return route.path
})

// 获取当前用户信息
const loadCurrentUser = () => {
  currentUser.value = getCurrentAdmin()
}

// 格式化日期
const formatDate = (date) => {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 处理菜单选择
const handleMenuSelect = (index) => {
  console.log('选择菜单:', index)
}

// 处理下拉菜单命令
const handleCommand = async (command) => {
  if (command === 'logout') {
    try {
      await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })

      // 执行登出
      await adminLogout()
      ElMessage.success('已退出登录')

      // 跳转到登录页
      router.push('/login')
    } catch (error) {
      // 用户取消操作
      if (error !== 'cancel') {
        console.error('退出登录失败:', error)
      }
    }
  } else if (command === 'profile') {
    ElMessage.info('个人资料功能开发中...')
  } else if (command === 'settings') {
    ElMessage.info('账户设置功能开发中...')
  }
}

onMounted(() => {
  loadCurrentUser()
})
</script>

<style scoped>
.dashboard-container {
  height: 100vh;
  background: #f5f5f5;
}

.el-container {
  height: 100%;
}

/* 头部样式 */
.header {
  background: white;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 60px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
}

.logo-text {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.welcome-text {
  font-size: 14px;
  color: #666;
}

.user-dropdown {
  margin-left: 8px;
}

.user-btn {
  color: #666;
  padding: 8px;
}

.user-btn:hover {
  color: #409eff;
}

.dropdown-icon {
  margin-left: 4px;
  font-size: 12px;
}

/* 侧边栏样式 */
.sidebar {
  background: white;
  border-right: 1px solid #e4e7ed;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.sidebar-menu {
  border: none;
  background: white;
  padding: 10px 0;
}

.menu-item {
  margin: 2px 10px;
  border-radius: 4px;
}

:deep(.el-menu-item) {
  color: #333;
  border-radius: 4px;
  margin: 2px 10px;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
}

:deep(.el-menu-item:hover) {
  background: #ecf5ff;
  color: #409eff;
}

:deep(.el-menu-item.is-active) {
  background: #409eff;
  color: white;
}

:deep(.el-menu-item .el-icon) {
  margin-right: 8px;
  font-size: 16px;
}

/* 主内容区样式 */
.main-content {
  background: #f5f5f5;
  padding: 20px;
  overflow-y: auto;
  min-height: calc(100vh - 60px);
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stats-card {
  border: none;
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
}

.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 24px;
}

.stats-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 24px;
}

.users-icon {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.content-icon {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.revenue-icon {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.activity-icon {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 8px 0;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #718096;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.stats-trend {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 8px;
}

.stats-trend.positive {
  color: #38a169;
  background: rgba(56, 161, 105, 0.1);
}

/* 欢迎区域 */
.welcome-section {
  margin-bottom: 32px;
}

.welcome-card {
  border: none;
  border-radius: 20px;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.welcome-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40px;
}

.welcome-text {
  flex: 1;
}

.welcome-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 12px 0;
  color: #fff;
}

.welcome-subtitle {
  font-size: 16px;
  margin: 0 0 24px 0;
  color: rgba(255, 255, 255, 0.9);
}

.welcome-info {
  display: flex;
  gap: 32px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #fff;
  font-weight: 600;
}

.welcome-illustration {
  opacity: 0.3;
}

.welcome-icon {
  color: rgba(255, 255, 255, 0.5);
}

/* 快速操作区域 */
.quick-actions {
  margin-bottom: 32px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 20px 0;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.action-card {
  border: none;
  border-radius: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
  background: #fff;
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
}

.action-content {
  text-align: center;
  padding: 32px 20px;
}

.action-icon {
  color: #667eea;
  margin-bottom: 16px;
  background: rgba(102, 126, 234, 0.1);
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.action-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 8px 0;
}

.action-content p {
  font-size: 14px;
  color: #718096;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 0 16px;
  }

  .header-center {
    display: none;
  }

  .main-content {
    padding: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .welcome-content {
    flex-direction: column;
    text-align: center;
    padding: 32px 24px;
  }

  .welcome-illustration {
    margin-top: 20px;
  }

  .actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
  }
}

/* 滚动条样式 */
.main-content::-webkit-scrollbar {
  width: 6px;
}

.main-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a67d8, #6b46c1);
}
</style>
