import{Q as T,_ as q,r as d,q as O,b as n,R as Q,o as V,c as R,e as s,f as e,w as t,E as g,v as h,i as W,h as i,j as m,S as A,x as F,T as U,U as G,l as H,V as D,P as J,O as X}from"./index-71d446cc.js";const Y=C=>T("/users/list",C);const Z={class:"user-list-container"},ee={class:"page-header"},te={class:"header-content"},ae={class:"stats-section"},se={class:"stat-item"},le={class:"stat-number"},oe={class:"toolbar"},ne={class:"search-section"},ie={class:"action-section"},re={class:"nickname"},ce={class:"time-info"},de={class:"time-info"},_e={__name:"UserListView",setup(C){const _=d(!1),k=d([]),w=d(0),u=d(1),b=d(50),x=d(""),p=async()=>{_.value=!0;try{const a=await Y({page:u.value,page_size:b.value});a.code===0?(k.value=a.data.users,w.value=a.data.total):g.error(a.message||"获取用户列表失败")}catch(a){console.error("获取用户列表失败:",a),g.error("获取用户列表失败")}finally{_.value=!1}},y=()=>{u.value=1,p()},L=()=>{p()},S=a=>{b.value=a,u.value=1,p()},B=a=>{u.value=a,p()},N=a=>{g.info(`查看用户: ${a.phone}`)},$=a=>{g.info(`编辑用户: ${a.phone}`)},z=a=>a?new Date(a).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"-";return O(()=>{p()}),(a,l)=>{const f=n("el-card"),c=n("el-icon"),E=n("el-input"),v=n("el-button"),r=n("el-table-column"),K=n("el-avatar"),j=n("el-table"),I=n("el-pagination"),M=Q("loading");return V(),R("div",Z,[s("div",ee,[s("div",te,[l[2]||(l[2]=s("div",{class:"title-section"},[s("h1",{class:"page-title"},"用户管理"),s("p",{class:"page-subtitle"},"管理系统中的所有用户信息")],-1)),s("div",ae,[e(f,{class:"stats-card"},{default:t(()=>[s("div",se,[s("div",le,h(w.value),1),l[1]||(l[1]=s("div",{class:"stat-label"},"总用户数",-1))])]),_:1})])])]),e(f,{class:"toolbar-card",shadow:"never"},{default:t(()=>[s("div",oe,[s("div",ne,[e(E,{modelValue:x.value,"onUpdate:modelValue":l[0]||(l[0]=o=>x.value=o),placeholder:"搜索用户手机号或昵称",class:"search-input",clearable:"",onClear:y,onKeyup:W(y,["enter"])},{prefix:t(()=>[e(c,null,{default:t(()=>[e(i(U))]),_:1})]),_:1},8,["modelValue"]),e(v,{type:"primary",onClick:y,loading:_.value},{default:t(()=>[e(c,null,{default:t(()=>[e(i(U))]),_:1}),l[3]||(l[3]=m(" 搜索 "))]),_:1,__:[3]},8,["loading"])]),s("div",ie,[e(v,{onClick:L,loading:_.value},{default:t(()=>[e(c,null,{default:t(()=>[e(i(G))]),_:1}),l[4]||(l[4]=m(" 刷新 "))]),_:1,__:[4]},8,["loading"])])])]),_:1}),e(f,{class:"table-card",shadow:"never"},{default:t(()=>[A((V(),F(j,{data:k.value,stripe:"",style:{width:"100%"},"header-cell-style":{background:"#fafafa",color:"#262626",fontWeight:"600",borderBottom:"1px solid #f0f0f0"},"row-style":{height:"60px"},"empty-text":"暂无用户数据"},{default:t(()=>[e(r,{prop:"id",label:"用户ID",width:"80",align:"center"}),e(r,{prop:"phone",label:"手机号",width:"140"}),e(r,{prop:"nickname",label:"昵称",width:"120"},{default:t(o=>[s("span",re,h(o.row.nickname||"未设置"),1)]),_:1}),e(r,{label:"头像",width:"80",align:"center"},{default:t(o=>[e(K,{size:40,src:o.row.avatar,class:"user-avatar"},{default:t(()=>[e(c,null,{default:t(()=>[e(i(H))]),_:1})]),_:2},1032,["src"])]),_:1}),e(r,{prop:"created_at",label:"注册时间",width:"180"},{default:t(o=>[s("div",ce,[e(c,{class:"time-icon"},{default:t(()=>[e(i(D))]),_:1}),s("span",null,h(z(o.row.created_at)),1)])]),_:1}),e(r,{prop:"updated_at",label:"最后更新",width:"180"},{default:t(o=>[s("div",de,[e(c,{class:"time-icon"},{default:t(()=>[e(i(D))]),_:1}),s("span",null,h(z(o.row.updated_at)),1)])]),_:1}),e(r,{label:"操作",width:"160",fixed:"right",align:"center"},{default:t(o=>[e(v,{type:"primary",size:"small",onClick:P=>N(o.row),icon:i(J)},{default:t(()=>l[5]||(l[5]=[m(" 查看 ")])),_:2,__:[5]},1032,["onClick","icon"]),e(v,{type:"warning",size:"small",onClick:P=>$(o.row),icon:i(X)},{default:t(()=>l[6]||(l[6]=[m(" 编辑 ")])),_:2,__:[6]},1032,["onClick","icon"])]),_:1})]),_:1},8,["data"])),[[M,_.value]])]),_:1}),e(f,{class:"pagination-card",shadow:"never"},{default:t(()=>[e(I,{"current-page":u.value,"page-size":b.value,"page-sizes":[10,20,50,100],total:w.value,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:S,onCurrentChange:B,background:""},null,8,["current-page","page-size","total"])]),_:1})])}}},pe=q(_e,[["__scopeId","data-v-07938669"]]);export{pe as default};
