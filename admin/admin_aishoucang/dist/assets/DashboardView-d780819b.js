import{_ as Q,u as W,n as X,r as k,p as Y,q as Z,b as a,o as x,c as $,f as s,w as e,t as ss,e as l,h as o,j as d,v as p,x as M,y as ts,z as es,E as h,A as V,B as ls,C as v,l as b,D as os,F as ns,G as as,H as ds,I as is,J as N,K as f,L as us,M as _s,N as cs,O as rs,P as fs}from"./index-71d446cc.js";const ms={class:"dashboard-container"},ps={class:"header-left"},vs={class:"logo-section"},bs={class:"header-center"},ws={class:"header-right"},gs={class:"header-actions"},xs={class:"user-info"},hs={class:"user-details"},ys={class:"user-name"},zs={class:"sidebar-header"},Cs={class:"stats-grid"},Bs={class:"stats-content"},Ds={class:"stats-icon users-icon"},ks={class:"stats-info"},Ms={class:"stats-trend positive"},Vs={class:"stats-content"},Ns={class:"stats-icon content-icon"},Ss={class:"stats-info"},Es={class:"stats-trend positive"},Is={class:"stats-content"},Ls={class:"stats-icon revenue-icon"},Ts={class:"stats-info"},qs={class:"stats-trend positive"},As={class:"stats-content"},Rs={class:"stats-icon activity-icon"},Us={class:"stats-info"},js={class:"stats-trend positive"},Fs={class:"welcome-section"},Gs={class:"welcome-content"},Hs={class:"welcome-text"},Js={class:"welcome-title"},Ks={class:"welcome-info"},Os={class:"info-item"},Ps={class:"info-value"},Qs={class:"info-item"},Ws={class:"info-value"},Xs={class:"welcome-illustration"},Ys={class:"quick-actions"},Zs={class:"actions-grid"},$s={class:"action-content"},st={class:"action-content"},tt={class:"action-content"},et={class:"action-content"},lt={__name:"DashboardView",setup(ot){const S=W(),E=X(),m=k(null),c=k(!1),I=Y(()=>E.path),L=()=>{m.value=ss()},T=()=>{c.value=!c.value},q=u=>u.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}),A=u=>{console.log("选择菜单:",u)},R=async u=>{if(u==="logout")try{await ts.confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await es(),h.success("已退出登录"),S.push("/login")}catch(t){t!=="cancel"&&console.error("退出登录失败:",t)}else u==="profile"?h.info("个人资料功能开发中..."):u==="settings"&&h.info("账户设置功能开发中...")};return Z(()=>{L()}),(u,t)=>{const n=a("el-icon"),y=a("el-breadcrumb-item"),U=a("el-breadcrumb"),w=a("el-button"),j=a("el-badge"),z=a("el-tooltip"),F=a("el-avatar"),g=a("el-dropdown-item"),G=a("el-dropdown-menu"),H=a("el-dropdown"),J=a("el-header"),_=a("el-menu-item"),C=a("el-sub-menu"),K=a("el-menu"),O=a("el-aside"),i=a("el-card"),P=a("el-main"),B=a("el-container");return x(),$("div",ms,[s(B,null,{default:e(()=>[s(J,{class:"header"},{default:e(()=>[l("div",ps,[l("div",vs,[s(n,{class:"logo-icon",size:"28"},{default:e(()=>[s(o(V))]),_:1}),t[0]||(t[0]=l("h1",{class:"logo-text"},"爱收藏管理后台",-1))])]),l("div",bs,[s(U,{separator:"/"},{default:e(()=>[s(y,null,{default:e(()=>t[1]||(t[1]=[d("首页")])),_:1,__:[1]}),s(y,null,{default:e(()=>t[2]||(t[2]=[d("仪表盘")])),_:1,__:[2]})]),_:1})]),l("div",ws,[l("div",gs,[s(z,{content:"消息通知",placement:"bottom"},{default:e(()=>[s(j,{value:3,class:"notification-badge"},{default:e(()=>[s(w,{circle:"",size:"large",class:"action-btn"},{default:e(()=>[s(n,null,{default:e(()=>[s(o(ls))]),_:1})]),_:1})]),_:1})]),_:1}),s(z,{content:"设置",placement:"bottom"},{default:e(()=>[s(w,{circle:"",size:"large",class:"action-btn"},{default:e(()=>[s(n,null,{default:e(()=>[s(o(v))]),_:1})]),_:1})]),_:1}),s(H,{onCommand:R,class:"user-dropdown"},{dropdown:e(()=>[s(G,null,{default:e(()=>[s(g,{command:"profile"},{default:e(()=>[s(n,null,{default:e(()=>[s(o(b))]),_:1}),t[4]||(t[4]=d(" 个人资料 "))]),_:1,__:[4]}),s(g,{command:"settings"},{default:e(()=>[s(n,null,{default:e(()=>[s(o(v))]),_:1}),t[5]||(t[5]=d(" 账户设置 "))]),_:1,__:[5]}),s(g,{divided:"",command:"logout"},{default:e(()=>[s(n,null,{default:e(()=>[s(o(os))]),_:1}),t[6]||(t[6]=d(" 退出登录 "))]),_:1,__:[6]})]),_:1})]),default:e(()=>{var r;return[l("div",xs,[s(F,{size:36,class:"user-avatar"},{default:e(()=>[s(n,null,{default:e(()=>[s(o(b))]),_:1})]),_:1}),l("div",hs,[l("span",ys,p(((r=m.value)==null?void 0:r.account)||"管理员"),1),t[3]||(t[3]=l("span",{class:"user-role"},"超级管理员",-1))]),s(n,{class:"dropdown-icon"},{default:e(()=>[s(o(ns))]),_:1})])]}),_:1})])])]),_:1}),s(B,null,{default:e(()=>[s(O,{width:c.value?"64px":"240px",class:"sidebar"},{default:e(()=>[l("div",zs,[s(w,{onClick:T,circle:"",size:"small",class:"collapse-btn",type:"primary"},{default:e(()=>[s(n,null,{default:e(()=>[c.value?(x(),M(o(ds),{key:1})):(x(),M(o(as),{key:0}))]),_:1})]),_:1})]),s(K,{"default-active":I.value,class:"sidebar-menu",collapse:c.value,"collapse-transition":!1,onSelect:A,router:""},{default:e(()=>[s(_,{index:"/",class:"menu-item"},{title:e(()=>t[7]||(t[7]=[d("仪表盘")])),default:e(()=>[s(n,null,{default:e(()=>[s(o(is))]),_:1})]),_:1}),s(C,{index:"users",class:"menu-item"},{title:e(()=>[s(n,null,{default:e(()=>[s(o(b))]),_:1}),t[8]||(t[8]=l("span",null,"用户管理",-1))]),default:e(()=>[s(_,{index:"/users/list"},{default:e(()=>t[9]||(t[9]=[d("用户列表")])),_:1,__:[9]}),s(_,{index:"/users/roles"},{default:e(()=>t[10]||(t[10]=[d("角色权限")])),_:1,__:[10]})]),_:1}),s(C,{index:"content",class:"menu-item"},{title:e(()=>[s(n,null,{default:e(()=>[s(o(N))]),_:1}),t[11]||(t[11]=l("span",null,"内容管理",-1))]),default:e(()=>[s(_,{index:"/content/list"},{default:e(()=>t[12]||(t[12]=[d("内容列表")])),_:1,__:[12]}),s(_,{index:"/content/category"},{default:e(()=>t[13]||(t[13]=[d("分类管理")])),_:1,__:[13]})]),_:1}),s(_,{index:"/analytics",class:"menu-item"},{title:e(()=>t[14]||(t[14]=[d("数据分析")])),default:e(()=>[s(n,null,{default:e(()=>[s(o(f))]),_:1})]),_:1}),s(_,{index:"/settings",class:"menu-item"},{title:e(()=>t[15]||(t[15]=[d("系统设置")])),default:e(()=>[s(n,null,{default:e(()=>[s(o(v))]),_:1})]),_:1})]),_:1},8,["default-active","collapse"])]),_:1},8,["width"]),s(P,{class:"main-content"},{default:e(()=>[l("div",Cs,[s(i,{class:"stats-card",shadow:"hover"},{default:e(()=>[l("div",Bs,[l("div",Ds,[s(n,{size:"24"},{default:e(()=>[s(o(b))]),_:1})]),l("div",ks,[t[17]||(t[17]=l("h3",{class:"stats-number"},"1,234",-1)),t[18]||(t[18]=l("p",{class:"stats-label"},"总用户数",-1)),l("span",Ms,[s(n,null,{default:e(()=>[s(o(f))]),_:1}),t[16]||(t[16]=d(" +12.5% "))])])])]),_:1}),s(i,{class:"stats-card",shadow:"hover"},{default:e(()=>[l("div",Vs,[l("div",Ns,[s(n,{size:"24"},{default:e(()=>[s(o(N))]),_:1})]),l("div",Ss,[t[20]||(t[20]=l("h3",{class:"stats-number"},"5,678",-1)),t[21]||(t[21]=l("p",{class:"stats-label"},"内容总数",-1)),l("span",Es,[s(n,null,{default:e(()=>[s(o(f))]),_:1}),t[19]||(t[19]=d(" +8.3% "))])])])]),_:1}),s(i,{class:"stats-card",shadow:"hover"},{default:e(()=>[l("div",Is,[l("div",Ls,[s(n,{size:"24"},{default:e(()=>[s(o(us))]),_:1})]),l("div",Ts,[t[23]||(t[23]=l("h3",{class:"stats-number"},"¥89,012",-1)),t[24]||(t[24]=l("p",{class:"stats-label"},"本月收入",-1)),l("span",qs,[s(n,null,{default:e(()=>[s(o(f))]),_:1}),t[22]||(t[22]=d(" +15.2% "))])])])]),_:1}),s(i,{class:"stats-card",shadow:"hover"},{default:e(()=>[l("div",As,[l("div",Rs,[s(n,{size:"24"},{default:e(()=>[s(o(_s))]),_:1})]),l("div",Us,[t[26]||(t[26]=l("h3",{class:"stats-number"},"98.5%",-1)),t[27]||(t[27]=l("p",{class:"stats-label"},"系统活跃度",-1)),l("span",js,[s(n,null,{default:e(()=>[s(o(f))]),_:1}),t[25]||(t[25]=d(" +2.1% "))])])])]),_:1})]),l("div",Fs,[s(i,{class:"welcome-card",shadow:"never"},{default:e(()=>{var r,D;return[l("div",Gs,[l("div",Hs,[l("h2",Js,"欢迎回来，"+p(((r=m.value)==null?void 0:r.account)||"管理员")+"！",1),t[30]||(t[30]=l("p",{class:"welcome-subtitle"},"今天是个美好的一天，让我们开始工作吧",-1)),l("div",Ks,[l("div",Os,[t[28]||(t[28]=l("span",{class:"info-label"},"用户ID:",-1)),l("span",Ps,p((D=m.value)==null?void 0:D.user_id),1)]),l("div",Qs,[t[29]||(t[29]=l("span",{class:"info-label"},"最后登录:",-1)),l("span",Ws,p(q(new Date)),1)])])]),l("div",Xs,[s(n,{size:"120",class:"welcome-icon"},{default:e(()=>[s(o(V))]),_:1})])])]}),_:1})]),l("div",Ys,[t[39]||(t[39]=l("h3",{class:"section-title"},"快速操作",-1)),l("div",Zs,[s(i,{class:"action-card",shadow:"hover"},{default:e(()=>[l("div",$s,[s(n,{class:"action-icon",size:"32"},{default:e(()=>[s(o(cs))]),_:1}),t[31]||(t[31]=l("h4",null,"添加用户",-1)),t[32]||(t[32]=l("p",null,"快速添加新用户",-1))])]),_:1}),s(i,{class:"action-card",shadow:"hover"},{default:e(()=>[l("div",st,[s(n,{class:"action-icon",size:"32"},{default:e(()=>[s(o(rs))]),_:1}),t[33]||(t[33]=l("h4",null,"内容管理",-1)),t[34]||(t[34]=l("p",null,"管理系统内容",-1))])]),_:1}),s(i,{class:"action-card",shadow:"hover"},{default:e(()=>[l("div",tt,[s(n,{class:"action-icon",size:"32"},{default:e(()=>[s(o(fs))]),_:1}),t[35]||(t[35]=l("h4",null,"数据报表",-1)),t[36]||(t[36]=l("p",null,"查看详细报表",-1))])]),_:1}),s(i,{class:"action-card",shadow:"hover"},{default:e(()=>[l("div",et,[s(n,{class:"action-icon",size:"32"},{default:e(()=>[s(o(v))]),_:1}),t[37]||(t[37]=l("h4",null,"系统设置",-1)),t[38]||(t[38]=l("p",null,"配置系统参数",-1))])]),_:1})])])]),_:1})]),_:1})]),_:1})])}}},at=Q(lt,[["__scopeId","data-v-cfa9270d"]]);export{at as default};
