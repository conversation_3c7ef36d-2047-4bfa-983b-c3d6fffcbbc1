{"version": 3, "sources": ["../../../packages/locale/lang/zh-cn.ts"], "sourcesContent": ["export default {\n  name: 'zh-cn',\n  el: {\n    breadcrumb: {\n      label: '面包屑',\n    },\n    colorpicker: {\n      confirm: '确定',\n      clear: '清空',\n      defaultLabel: '颜色选择器',\n      description: '当前颜色 {color}，按 Enter 键选择新颜色',\n      alphaLabel: '选择透明度的值',\n    },\n    datepicker: {\n      now: '此刻',\n      today: '今天',\n      cancel: '取消',\n      clear: '清空',\n      confirm: '确定',\n      dateTablePrompt: '使用方向键与 Enter 键可选择日期',\n      monthTablePrompt: '使用方向键与 Enter 键可选择月份',\n      yearTablePrompt: '使用方向键与 Enter 键可选择年份',\n      selectedDate: '已选日期',\n      selectDate: '选择日期',\n      selectTime: '选择时间',\n      startDate: '开始日期',\n      startTime: '开始时间',\n      endDate: '结束日期',\n      endTime: '结束时间',\n      prevYear: '前一年',\n      nextYear: '后一年',\n      prevMonth: '上个月',\n      nextMonth: '下个月',\n      year: '年',\n      month1: '1 月',\n      month2: '2 月',\n      month3: '3 月',\n      month4: '4 月',\n      month5: '5 月',\n      month6: '6 月',\n      month7: '7 月',\n      month8: '8 月',\n      month9: '9 月',\n      month10: '10 月',\n      month11: '11 月',\n      month12: '12 月',\n      // week: '周次',\n      weeks: {\n        sun: '日',\n        mon: '一',\n        tue: '二',\n        wed: '三',\n        thu: '四',\n        fri: '五',\n        sat: '六',\n      },\n      weeksFull: {\n        sun: '星期日',\n        mon: '星期一',\n        tue: '星期二',\n        wed: '星期三',\n        thu: '星期四',\n        fri: '星期五',\n        sat: '星期六',\n      },\n      months: {\n        jan: '一月',\n        feb: '二月',\n        mar: '三月',\n        apr: '四月',\n        may: '五月',\n        jun: '六月',\n        jul: '七月',\n        aug: '八月',\n        sep: '九月',\n        oct: '十月',\n        nov: '十一月',\n        dec: '十二月',\n      },\n    },\n    inputNumber: {\n      decrease: '减少数值',\n      increase: '增加数值',\n    },\n    select: {\n      loading: '加载中',\n      noMatch: '无匹配数据',\n      noData: '无数据',\n      placeholder: '请选择',\n    },\n    dropdown: {\n      toggleDropdown: '切换下拉选项',\n    },\n    mention: {\n      loading: '加载中',\n    },\n    cascader: {\n      noMatch: '无匹配数据',\n      loading: '加载中',\n      placeholder: '请选择',\n      noData: '暂无数据',\n    },\n    pagination: {\n      goto: '前往',\n      pagesize: '条/页',\n      total: '共 {total} 条',\n      pageClassifier: '页',\n      page: '页',\n      prev: '上一页',\n      next: '下一页',\n      currentPage: '第 {pager} 页',\n      prevPages: '向前 {pager} 页',\n      nextPages: '向后 {pager} 页',\n      deprecationWarning:\n        '你使用了一些已被废弃的用法，请参考 el-pagination 的官方文档',\n    },\n    dialog: {\n      close: '关闭此对话框',\n    },\n    drawer: {\n      close: '关闭此对话框',\n    },\n    messagebox: {\n      title: '提示',\n      confirm: '确定',\n      cancel: '取消',\n      error: '输入的数据不合法!',\n      close: '关闭此对话框',\n    },\n    upload: {\n      deleteTip: '按 Delete 键可删除',\n      delete: '删除',\n      preview: '查看图片',\n      continue: '继续上传',\n    },\n    slider: {\n      defaultLabel: '滑块介于 {min} 至 {max}',\n      defaultRangeStartLabel: '选择起始值',\n      defaultRangeEndLabel: '选择结束值',\n    },\n    table: {\n      emptyText: '暂无数据',\n      confirmFilter: '筛选',\n      resetFilter: '重置',\n      clearFilter: '全部',\n      sumText: '合计',\n    },\n    tour: {\n      next: '下一步',\n      previous: '上一步',\n      finish: '结束导览',\n    },\n    tree: {\n      emptyText: '暂无数据',\n    },\n    transfer: {\n      noMatch: '无匹配数据',\n      noData: '无数据',\n      titles: ['列表 1', '列表 2'],\n      filterPlaceholder: '请输入搜索内容',\n      noCheckedFormat: '共 {total} 项',\n      hasCheckedFormat: '已选 {checked}/{total} 项',\n    },\n    image: {\n      error: '加载失败',\n    },\n    pageHeader: {\n      title: '返回',\n    },\n    popconfirm: {\n      confirmButtonText: '确定',\n      cancelButtonText: '取消',\n    },\n    carousel: {\n      leftArrow: '上一张幻灯片',\n      rightArrow: '下一张幻灯片',\n      indicator: '幻灯片切换至索引 {index}',\n    },\n  },\n}\n"], "mappings": ";;;AAAA,IAAA,OAAe;EACb,MAAM;EACN,IAAI;IACF,YAAY;MACV,OAAO;IACb;IACI,aAAa;MACX,SAAS;MACT,OAAO;MACP,cAAc;MACd,aAAa;MACb,YAAY;IAClB;IACI,YAAY;MACV,KAAK;MACL,OAAO;MACP,QAAQ;MACR,OAAO;MACP,SAAS;MACT,iBAAiB;MACjB,kBAAkB;MAClB,iBAAiB;MACjB,cAAc;MACd,YAAY;MACZ,YAAY;MACZ,WAAW;MACX,WAAW;MACX,SAAS;MACT,SAAS;MACT,UAAU;MACV,UAAU;MACV,WAAW;MACX,WAAW;MACX,MAAM;MACN,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,QAAQ;MACR,SAAS;MACT,SAAS;MACT,SAAS;MACT,OAAO;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;MACb;MACM,WAAW;QACT,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;MACb;MACM,QAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;MACb;IACA;IACI,aAAa;MACX,UAAU;MACV,UAAU;IAChB;IACI,QAAQ;MACN,SAAS;MACT,SAAS;MACT,QAAQ;MACR,aAAa;IACnB;IACI,UAAU;MACR,gBAAgB;IACtB;IACI,SAAS;MACP,SAAS;IACf;IACI,UAAU;MACR,SAAS;MACT,SAAS;MACT,aAAa;MACb,QAAQ;IACd;IACI,YAAY;MACV,MAAM;MACN,UAAU;MACV,OAAO;MACP,gBAAgB;MAChB,MAAM;MACN,MAAM;MACN,MAAM;MACN,aAAa;MACb,WAAW;MACX,WAAW;MACX,oBAAoB;IAC1B;IACI,QAAQ;MACN,OAAO;IACb;IACI,QAAQ;MACN,OAAO;IACb;IACI,YAAY;MACV,OAAO;MACP,SAAS;MACT,QAAQ;MACR,OAAO;MACP,OAAO;IACb;IACI,QAAQ;MACN,WAAW;MACX,QAAQ;MACR,SAAS;MACT,UAAU;IAChB;IACI,QAAQ;MACN,cAAc;MACd,wBAAwB;MACxB,sBAAsB;IAC5B;IACI,OAAO;MACL,WAAW;MACX,eAAe;MACf,aAAa;MACb,aAAa;MACb,SAAS;IACf;IACI,MAAM;MACJ,MAAM;MACN,UAAU;MACV,QAAQ;IACd;IACI,MAAM;MACJ,WAAW;IACjB;IACI,UAAU;MACR,SAAS;MACT,QAAQ;MACR,QAAQ,CAAC,QAAkB,MAAgB;MAC3C,mBAAmB;MACnB,iBAAiB;MACjB,kBAAkB;IACxB;IACI,OAAO;MACL,OAAO;IACb;IACI,YAAY;MACV,OAAO;IACb;IACI,YAAY;MACV,mBAAmB;MACnB,kBAAkB;IACxB;IACI,UAAU;MACR,WAAW;MACX,YAAY;MACZ,WAAW;IACjB;EACA;AACA;", "names": []}